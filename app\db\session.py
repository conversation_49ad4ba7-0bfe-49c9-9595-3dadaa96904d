from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import settings

engine = create_engine(str(settings.DATABASE_URL))
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_callback_db():
    """
    为回调函数创建数据库会话的辅助函数

    Returns:
        Session: 数据库会话对象

    Note:
        调用者需要负责关闭会话
    """
    return SessionLocal()


from contextlib import contextmanager
from typing import Generator

@contextmanager
def callback_db_session() -> Generator[SessionLocal, None, None]:
    """
    为回调函数提供数据库会话的上下文管理器

    Usage:
        async def my_callback():
            with callback_db_session() as db:
                # 使用 db 进行数据库操作
                # 会话会自动提交和关闭
                pass

    Yields:
        Session: 数据库会话对象
    """
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()
