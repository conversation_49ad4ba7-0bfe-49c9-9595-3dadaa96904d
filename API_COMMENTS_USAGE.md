# 整体点评接口使用说明

## 接口概述

新增的整体点评接口用于根据练习情况ID生成AI整体点评，通过分析发言历史并生成点评内容。

## 接口详情

### 请求信息
- **方法**: POST
- **路径**: `/api/v1/scene/comments`
- **内容类型**: application/json

### 请求参数

```json
{
  "elid": 123
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| elid   | int  | 是   | 练习情况ID |

### 响应信息

- **成功响应**: 200 OK
- **内容类型**: text/event-stream
- **响应格式**: 流式返回AI生成的整体点评内容

### 权限要求

- 需要有效的用户身份令牌
- 只能处理当前租户下的练习情况
- 只能处理属于当前用户的练习情况

## 业务逻辑

### Step 1: 验证权限
- 验证练习情况是否存在
- 验证是否属于当前用户
- 验证是否属于当前租户

### Step 2: 构造发言历史
- 根据elid查询tnt_scene_speech记录，按id从小到大排序
- 通过cid关联tnt_character表获取角色名称
- 每条记录构造格式："ctime\tcharacter_name：content"
- 拼接所有记录形成完整的会议记录

### Step 3: 调用AI服务
- 使用机器人配置key=BOT_KEY_ZHCMTS
- 传入构造的发言历史作为messages
- 传入当前用户姓名作为variables中的student字段

### Step 4: 保存点评内容
- 成功获取完整点评内容后
- 更新tnt_exercise_log表中对应记录的comments字段

## 错误处理

### 404 错误
- 练习情况不存在
- 无权访问该练习情况（不属于当前用户或租户）
- 没有找到相关的发言记录

### 500 错误
- AI服务调用失败
- 数据库操作失败
- 机器人配置不存在

## 使用示例

### cURL 示例

```bash
curl -X POST "http://localhost:8000/api/v1/scene/comments" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"elid": 123}'
```

### JavaScript 示例

```javascript
// 使用fetch API调用接口
const response = await fetch('/api/v1/scene/comments', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ elid: 123 })
});

// 处理流式响应
const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  console.log('收到数据:', chunk);
}
```

## 注意事项

1. **流式响应**: 接口返回流式响应，客户端需要正确处理Server-Sent Events格式
2. **异步保存**: 点评内容的保存是在AI响应完成后异步进行的
3. **错误恢复**: 如果保存失败，不会影响AI响应的返回
4. **权限控制**: 严格的权限控制确保用户只能访问自己的数据
5. **机器人配置**: 需要确保BOT_KEY_ZHCMTS对应的机器人配置存在且有效

## 数据库影响

### 查询的表
- tnt_exercise_log: 验证练习情况
- tnt_scene_speech: 获取发言记录
- tnt_character: 获取角色名称
- tnt_bconf/sys_bconf: 获取机器人配置

### 更新的表
- tnt_exercise_log: 更新comments字段保存点评内容

## 相关配置

确保以下配置正确设置：
- BOT_KEY_ZHCMTS机器人配置
- AI服务的API端点和密钥
- 数据库连接配置
