import logging
from datetime import datetime
from typing import List, Optional

from sqlalchemy import and_
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from app.models.models import (
    TntClassExercise,
    TntExercise,
    TntExerciseLog,
    TntFramework,
    TntModule,
    TntQuestion,
    TntQuestionGuide,
    TntQuestionModule,
    TntStudent,
    TntTeacher,
    TntUnit,
    TntWorksheet,
    TntWorksheetAnswer,
    TntWorksheetAsm,
)
from app.schemas.worksheet import (
    FrameworkModuleResponse,
    FrameworkResponse,
    QuestionGuideResponse,
    UnitQuestionResponse,
    UnitQuestionsResponse,
    WorksheetBasicResponse,
    WorksheetDetailResponse,
    WorksheetUnitResponse,
    WorksheetUnitsResponse,
)
from app.utils.oss import get_oss_url

logger = logging.getLogger(__name__)


def get_worksheet_basic_info(
    db: Session, worksheet_id: int, class_id: int, current_user: TntStudent
) -> Optional[WorksheetBasicResponse]:
    """
    获取作业单基本信息

    Args:
        db: 数据库会话
        worksheet_id: 作业单ID
        class_id: 班级ID
        current_user: 当前用户

    Returns:
        作业单基本信息或None
    """
    # 创建预过滤的子查询
    worksheet_filtered = (
        db.query(TntWorksheet)
        .filter(
            and_(
                TntWorksheet.id == worksheet_id,
                TntWorksheet.tenant_id == current_user.tenant_id,
            )
        )
        .subquery("worksheet_filtered")
    )

    exercise_filtered = (
        db.query(TntExercise)
        .filter(
            and_(
                TntExercise.tenant_id == current_user.tenant_id,
                TntExercise.active == 1,
            )
        )
        .subquery("exercise_filtered")
    )

    exercise_log_filtered = (
        db.query(TntExerciseLog)
        .filter(
            and_(
                TntExerciseLog.cid == class_id,
                TntExerciseLog.sid == current_user.id,
                TntExerciseLog.tenant_id == current_user.tenant_id,
                TntExerciseLog.active == 1,
            )
        )
        .subquery("exercise_log_filtered")
    )

    class_exercise_filtered = (
        db.query(TntClassExercise)
        .filter(
            and_(
                TntClassExercise.cid == class_id,
                TntClassExercise.tenant_id == current_user.tenant_id,
            )
        )
        .subquery("class_exercise_filtered")
    )

    teacher_filtered = (
        db.query(TntTeacher)
        .filter(TntTeacher.tenant_id == current_user.tenant_id)
        .subquery("teacher_filtered")
    )

    # 使用子查询进行连接，选择具体的列
    worksheet_data = (
        db.query(
            worksheet_filtered.c.id.label("worksheet_id"),
            exercise_filtered.c.id.label("exercise_id"),
            exercise_filtered.c.title.label("exercise_title"),
            exercise_filtered.c.pic.label("exercise_pic"),
            exercise_filtered.c.intro.label("exercise_intro"),
            exercise_filtered.c.duration.label("exercise_duration"),
            exercise_filtered.c.bgtext.label("exercise_bgtext"),
            exercise_filtered.c.bgvideo.label("exercise_bgvideo"),
            exercise_log_filtered.c.id.label("log_id"),
            exercise_log_filtered.c.report.label("log_report"),
            exercise_log_filtered.c.btime.label("log_btime"),
            exercise_log_filtered.c.stime.label("log_stime"),
            exercise_log_filtered.c.utime.label("log_utime"),
            exercise_log_filtered.c.status.label("log_status"),
            teacher_filtered.c.id.label("teacher_id"),
            teacher_filtered.c.name.label("teacher_name"),
            teacher_filtered.c.avatar.label("teacher_avatar"),
        )
        .select_from(worksheet_filtered)
        .join(
            exercise_filtered,
            exercise_filtered.c.id == worksheet_filtered.c.eid,
        )
        .outerjoin(
            exercise_log_filtered,
            exercise_log_filtered.c.eid == exercise_filtered.c.id,
        )
        .outerjoin(
            class_exercise_filtered,
            class_exercise_filtered.c.eid == exercise_filtered.c.id,
        )
        .outerjoin(
            teacher_filtered,
            teacher_filtered.c.id == class_exercise_filtered.c.tid,
        )
        .first()
    )

    if not worksheet_data:
        return None

    # 构建响应
    response = WorksheetBasicResponse(
        title=worksheet_data.exercise_title,
        pic=get_oss_url(worksheet_data.exercise_pic)
        if worksheet_data.exercise_pic
        else None,
        intro=worksheet_data.exercise_intro,
        duration=worksheet_data.exercise_duration,
        bgtext=worksheet_data.exercise_bgtext,
        bgvideo=get_oss_url(worksheet_data.exercise_bgvideo)
        if worksheet_data.exercise_bgvideo
        else None,
        report=get_oss_url(worksheet_data.log_report)
        if worksheet_data.log_report
        else None,
        btime=worksheet_data.log_btime,
        stime=worksheet_data.log_stime,
        utime=worksheet_data.log_utime,
        status=worksheet_data.log_status
        if worksheet_data.log_status is not None
        else 0,
        eid=worksheet_data.exercise_id,
        elid=worksheet_data.log_id,
        tid=worksheet_data.teacher_id,
        tname=worksheet_data.teacher_name,
        tavatar=get_oss_url(worksheet_data.teacher_avatar)
        if worksheet_data.teacher_avatar
        else None,
    )

    return response


def get_worksheet_units(
    db: Session, worksheet_id: int, current_user: TntStudent
) -> Optional[WorksheetUnitsResponse]:
    """
    获取作业单单元列表

    Args:
        db: 数据库会话
        worksheet_id: 作业单ID
        current_user: 当前用户

    Returns:
        作业单单元列表或None
    """
    # 查询作业单和关联的单元信息
    units_data = (
        db.query(TntUnit)
        .join(
            TntWorksheet,
            and_(
                TntWorksheet.id == TntUnit.wid,
                TntWorksheet.tenant_id == current_user.tenant_id,
            ),
        )
        .filter(
            and_(
                TntUnit.wid == worksheet_id,
                TntUnit.tenant_id == current_user.tenant_id,
            )
        )
        .order_by(TntUnit.priority)
        .all()
    )

    if not units_data:
        return None

    # 构建单元列表
    unit_list = [
        WorksheetUnitResponse(
            id=unit.id,
            name=unit.name,
            bgtext=unit.bgtext,
            bgvideo=get_oss_url(unit.bgvideo) if unit.bgvideo else None,
        )
        for unit in units_data
    ]

    return WorksheetUnitsResponse(unit_list=unit_list)


def get_worksheet_detail(
    db: Session, worksheet_id: int, class_id: int, current_user: TntStudent
) -> Optional[WorksheetDetailResponse]:
    """
    获取作业单详情和单元列表

    Args:
        db: 数据库会话
        worksheet_id: 作业单ID
        current_user: 当前用户

    Returns:
        作业单详情或None
    """
    # 查询作业单和关联的练习信息，包含班级验证
    worksheet_data = (
        db.query(
            TntWorksheet,
            TntExercise,
            TntExerciseLog,
            TntUnit,
        )
        .select_from(TntWorksheet)
        .join(
            TntExercise,
            and_(
                TntExercise.id == TntWorksheet.eid,
                TntExercise.tenant_id == current_user.tenant_id,
                TntExercise.active == 1,
            ),
        )
        .outerjoin(
            TntExerciseLog,
            and_(
                TntExerciseLog.eid == TntExercise.id,
                TntExerciseLog.cid == class_id,
                TntExerciseLog.sid == current_user.id,
                TntExerciseLog.tenant_id == current_user.tenant_id,
                TntExerciseLog.active == 1,
            ),
        )
        .join(
            TntUnit,
            and_(
                TntUnit.wid == TntWorksheet.id,
                TntUnit.tenant_id == current_user.tenant_id,
            ),
        )
        .filter(
            and_(
                TntWorksheet.id == worksheet_id,
                TntWorksheet.tenant_id == current_user.tenant_id,
            )
        )
        .order_by(TntUnit.priority)
        .all()
    )

    if not worksheet_data:
        return None

    # 从第一行数据中提取基础信息
    first_row = worksheet_data[0]
    worksheet = first_row[0]
    exercise = first_row[1]
    exercise_log = first_row[2]

    # 构建单元列表
    unit_list = []
    seen_units = set()

    for worksheet, exercise, exercise_log, unit in worksheet_data:
        if unit.id not in seen_units:
            unit_response = WorksheetUnitResponse(
                id=unit.id,
                name=unit.name,
                bgtext=unit.bgtext,
                bgvideo=get_oss_url(unit.bgvideo) if unit.bgvideo else None,
            )
            unit_list.append(unit_response)
            seen_units.add(unit.id)

    # 构建响应
    response = WorksheetDetailResponse(
        title=exercise.title,
        pic=get_oss_url(exercise.pic) if exercise.pic else None,
        intro=exercise.intro,
        duration=exercise.duration,
        bgtext=exercise.bgtext,
        bgvideo=get_oss_url(exercise.bgvideo) if exercise.bgvideo else None,
        report=get_oss_url(exercise_log.report)
        if exercise_log and exercise_log.report
        else None,
        btime=exercise_log.btime if exercise_log else None,
        stime=exercise_log.stime if exercise_log else None,
        utime=exercise_log.utime if exercise_log else None,
        status=exercise_log.status if exercise_log else 0,
        unit_list=unit_list,
        eid=exercise.id,
        elid=exercise_log.id if exercise_log else None,
    )

    return response


def get_unit_questions(
    db: Session,
    worksheet_id: int,
    unit_id: int,
    current_user: TntStudent,
    elid: int,
) -> Optional[UnitQuestionsResponse]:
    """
    获取单元下的所有问题信息

    Args:
        db: 数据库会话
        worksheet_id: 作业单ID
        unit_id: 单元ID
        current_user: 当前用户
        elid: 练习情况ID

    Returns:
        单元问题列表或None
    """
    # 一次性查询所有相关数据，包括问题、答案、理论框架和指南
    all_data = (
        db.query(
            TntQuestion,
            TntWorksheetAnswer,
            TntWorksheetAsm.priority,
            TntFramework,
            TntModule,
            TntQuestionGuide,
        )
        .select_from(TntWorksheetAsm)
        .join(
            TntQuestion,
            and_(
                TntQuestion.id == TntWorksheetAsm.qid,
                TntQuestion.tenant_id == current_user.tenant_id,
                TntQuestion.active == 1,
            ),
        )
        .outerjoin(
            TntWorksheetAnswer,
            and_(
                TntWorksheetAnswer.qid == TntQuestion.id,
                TntWorksheetAnswer.tenant_id == current_user.tenant_id,
                TntWorksheetAnswer.elid == elid,
            ),
        )
        .outerjoin(
            TntQuestionModule,
            and_(
                TntQuestionModule.qid == TntQuestion.id,
                TntQuestionModule.tenant_id == current_user.tenant_id,
            ),
        )
        .outerjoin(
            TntModule,
            and_(
                TntModule.id == TntQuestionModule.mid,
                TntModule.tenant_id == current_user.tenant_id,
                TntModule.active == 1,
            ),
        )
        .outerjoin(
            TntFramework,
            and_(
                TntFramework.id == TntModule.fid,
                TntFramework.tenant_id == current_user.tenant_id,
                TntFramework.active == 1,
            ),
        )
        .outerjoin(
            TntQuestionGuide,
            and_(
                TntQuestionGuide.qid == TntQuestion.id,
                TntQuestionGuide.tenant_id == current_user.tenant_id,
            ),
        )
        .filter(
            and_(
                TntWorksheetAsm.wid == worksheet_id,
                TntWorksheetAsm.uid == unit_id,
                TntWorksheetAsm.tenant_id == current_user.tenant_id,
            )
        )
        .order_by(
            TntWorksheetAsm.priority,
            TntFramework.priority,
            TntModule.priority,
            TntQuestionGuide.priority,
        )
        .all()
    )

    if not all_data:
        return UnitQuestionsResponse(question_list=[])

    # 按问题ID分组处理数据
    questions_dict = {}
    for question, answer, priority, framework, module, guide in all_data:
        question_id = question.id

        # 初始化问题数据
        if question_id not in questions_dict:
            questions_dict[question_id] = {
                "question": question,
                "answer": answer,
                "priority": priority,
                "frameworks": {},
                "guides": [],
            }

        # 处理理论框架数据
        if framework and module:
            framework_id = framework.id
            if framework_id not in questions_dict[question_id]["frameworks"]:
                questions_dict[question_id]["frameworks"][framework_id] = {
                    "framework": framework,
                    "modules": [],
                }
            # 避免重复添加相同的模块
            module_ids = [
                m.id
                for m in questions_dict[question_id]["frameworks"][framework_id][
                    "modules"
                ]
            ]
            if module.id not in module_ids:
                questions_dict[question_id]["frameworks"][framework_id][
                    "modules"
                ].append(module)

        # 处理指南数据
        if guide:
            # 避免重复添加相同的指南
            guide_ids = [g.id for g in questions_dict[question_id]["guides"]]
            if guide.id not in guide_ids:
                questions_dict[question_id]["guides"].append(guide)

    # 构建响应数据
    question_list = []
    # 按优先级排序
    sorted_questions = sorted(questions_dict.values(), key=lambda x: x["priority"])

    for question_data in sorted_questions:
        question = question_data["question"]
        answer = question_data["answer"]

        # 构建理论框架列表
        framework_list = []
        for fw_data in question_data["frameworks"].values():
            framework = fw_data["framework"]
            modules = fw_data["modules"]

            module_list = [
                FrameworkModuleResponse(name=module.name) for module in modules
            ]

            framework_response = FrameworkResponse(
                name=framework.name,
                logo=get_oss_url(framework.logo) if framework.logo else None,
                module_list=module_list,
            )
            framework_list.append(framework_response)

        # 构建指南列表
        guide_list = [
            QuestionGuideResponse(title=guide.title, details=guide.details)
            for guide in sorted(question_data["guides"], key=lambda x: x.priority)
        ]

        # 构建问题响应
        question_response = UnitQuestionResponse(
            id=question.id,
            title=question.title,
            bgtext=question.bgtext,
            bgvideo=get_oss_url(question.bgvideo) if question.bgvideo else None,
            draft=answer.draft if answer else None,
            answer=answer.answer if answer else None,
            comment=answer.comment if answer else None,
            framework_list=framework_list,
            guide_list=guide_list,
        )
        question_list.append(question_response)

    return UnitQuestionsResponse(question_list=question_list)


def get_question_info_for_comment(
    db: Session, qid: int, current_user: TntStudent
) -> Optional[dict]:
    """
    获取问题的点评相关信息

    Args:
        db: 数据库会话
        qid: 问题ID
        current_user: 当前用户

    Returns:
        包含问题信息的字典或None
    """
    question = (
        db.query(TntQuestion)
        .filter(
            and_(
                TntQuestion.id == qid,
                TntQuestion.tenant_id == current_user.tenant_id,
                TntQuestion.active == 1,
            )
        )
        .first()
    )

    if not question:
        return None

    return {
        "bgtext": question.bgtext,
        "pv_skills": question.pv_skills,
        "pv_rules": question.pv_rules,
        "pv_formats": question.pv_formats,
    }


async def update_worksheet_answer_callback(
    db: Session, tenant_id: int, elid: int, qid: int, answer: str, full_content: str
) -> None:
    """
    更新数据库中的作答和点评信息

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        elid: 练习情况ID
        qid: 问题ID
        answer: 作答内容
        full_content: AI点评内容
    """
    # 查找现有记录
    existing_answer = (
        db.query(TntWorksheetAnswer)
        .filter(
            and_(
                TntWorksheetAnswer.tenant_id == tenant_id,
                TntWorksheetAnswer.elid == elid,
                TntWorksheetAnswer.qid == qid,
            )
        )
        .first()
    )

    if existing_answer:
        # 更新现有记录
        existing_answer.answer = answer
        existing_answer.comment = full_content
        existing_answer.draft = None # type: ignore
    else:
        # 创建新记录
        new_answer = TntWorksheetAnswer(
            tenant_id=tenant_id,
            elid=elid,
            qid=qid,
            answer=answer,
            comment=full_content,
            draft=None,
        )
        db.add(new_answer)

    db.commit()


def retry_question_answer(db: Session, tenant_id: int, elid: int, qid: int) -> bool:
    """
    重新练习问题：将answer值填到draft中，再将answer和comment设为null

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        elid: 练习情况ID
        qid: 问题ID

    Returns:
        bool: 操作是否成功
    """
    # 查找现有记录
    existing_answer = (
        db.query(TntWorksheetAnswer)
        .filter(
            and_(
                TntWorksheetAnswer.tenant_id == tenant_id,
                TntWorksheetAnswer.elid == elid,
                TntWorksheetAnswer.qid == qid,
            )
        )
        .first()
    )

    if not existing_answer:
        return False

    # 将answer值填到draft中，将answer和comment设为null
    existing_answer.draft = existing_answer.answer
    existing_answer.answer = None # type: ignore
    existing_answer.comment = None # type: ignore

    db.commit()
    return True


def batch_update_question_drafts(
    db: Session, tenant_id: int, elid: int, draft_items: List
) -> bool:
    """
    批量更新问题草稿，并更新练习记录的更新时间

    使用SQLAlchemy的批量操作来提高性能，避免N+1查询问题

    Args:
        db: 数据库会话
        tenant_id: 租户ID
        elid: 练习情况ID
        draft_items: 草稿项列表，每项包含qid和draft

    Returns:
        bool: 操作是否成功
    """
    if not draft_items:
        logger.warning(f"Empty draft_items for elid={elid}, tenant_id={tenant_id}")
        return True

    try:
        # 查找所有现有记录
        qids = [item.qid for item in draft_items]
        existing_answers = (
            db.query(TntWorksheetAnswer)
            .filter(
                and_(
                    TntWorksheetAnswer.tenant_id == tenant_id,
                    TntWorksheetAnswer.elid == elid,
                    TntWorksheetAnswer.qid.in_(qids),
                )
            )
            .all()
        )

        # 创建现有记录的映射
        existing_qids = {answer.qid for answer in existing_answers}

        # 准备批量更新数据
        update_mappings = []
        insert_mappings = []

        for item in draft_items:
            qid = item.qid
            draft = item.draft

            if qid in existing_qids:
                # 准备更新数据
                update_mappings.append({"qid": qid, "draft": draft})
            else:
                # 准备插入数据
                insert_mappings.append(
                    {
                        "tenant_id": tenant_id,
                        "elid": elid,
                        "qid": qid,
                        "draft": draft,
                        "answer": None,
                        "comment": None,
                    }
                )

        # 执行批量更新
        if update_mappings:
            # 为批量更新准备完整的映射数据（包含主键信息）
            update_data = []
            for existing_answer in existing_answers:
                for mapping in update_mappings:
                    if existing_answer.qid == mapping["qid"]:
                        update_data.append(
                            {"id": existing_answer.id, "draft": mapping["draft"]}
                        )
                        break

            if update_data:
                db.bulk_update_mappings(
                    TntWorksheetAnswer, update_data
                )

        # 执行批量插入
        if insert_mappings:
            db.bulk_insert_mappings(TntWorksheetAnswer, insert_mappings)

        # 更新练习记录的更新时间
        exercise_log = (
            db.query(TntExerciseLog)
            .filter(
                and_(
                    TntExerciseLog.id == elid,
                    TntExerciseLog.tenant_id == tenant_id,
                    TntExerciseLog.active == 1,
                )
            )
            .first()
        )

        if not exercise_log:
            return False

        exercise_log.utime = datetime.now()

        # 提交事务
        db.commit()
        return True

    except SQLAlchemyError as e:
        # 数据库相关错误
        db.rollback()
        logger.error(
            f"Database error in batch_update_question_drafts for elid={elid}: {str(e)}"
        )
        return False
    except Exception as e:
        # 其他未预期的错误
        db.rollback()
        logger.error(
            f"Unexpected error in batch_update_question_drafts for elid={elid}: {str(e)}"
        )
        return False
